[package]
name = "zygiskd"
authors = ["JingMatrix", "Nullptr"]
version = "1.0.0"
edition = "2024"
rust-version = "1.85"

[dependencies]
android_logger = "0.14"
anyhow = { version = "1.0", features = ["backtrace"] }
bitflags = { version = "2.6" }
konst = "0.3"
libc = "0.2"
log = "0.4"
memfd = "0.6"
num_enum = "0.7"
passfd = "0.1"
procfs = "0.17"
proc-maps = "0.3"

rustix = { version = "0.38", features = [ "fs", "net", "thread" ] }

[profile.dev]
strip = false
panic = "abort"

[profile.release]
strip = false
debug = true
panic = "abort"
opt-level = "z"
lto = true
