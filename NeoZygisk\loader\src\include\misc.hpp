#pragma once

#include <pthread.h>

#include <list>
#include <memory>
#include <string>
#include <string_view>

// Reference:
// https://cs.android.com/android/platform/superproject/main/+/main:system/core/libcutils/include/private/android_filesystem_config.h
#define AID_ISOLATED_START 90000 /* start of uids for fully isolated sandboxed processes */
#define AID_ISOLATED_END 99999   /* end of uids for fully isolated sandboxed processes */

#define DISALLOW_COPY_AND_MOVE(clazz)                                                              \
    clazz(const clazz &) = delete;                                                                 \
    clazz(clazz &&) = delete;

class mutex_guard {
    DISALLOW_COPY_AND_MOVE(mutex_guard)
public:
    explicit mutex_guard(pthread_mutex_t &m) : mutex(&m) { pthread_mutex_lock(mutex); }
    void unlock() {
        pthread_mutex_unlock(mutex);
        mutex = nullptr;
    }
    ~mutex_guard() {
        if (mutex) pthread_mutex_unlock(mutex);
    }

private:
    pthread_mutex_t *mutex;
};

using thread_entry = void *(*) (void *);
int new_daemon_thread(thread_entry entry, void *arg);

static inline bool str_contains(std::string_view s, std::string_view ss) {
    return s.find(ss) != std::string_view::npos;
}

template <typename T, typename Impl>
class stateless_allocator {
public:
    using value_type = T;
    T *allocate(size_t num) { return static_cast<T *>(Impl::allocate(sizeof(T) * num)); }
    void deallocate(T *ptr, size_t num) { Impl::deallocate(ptr, sizeof(T) * num); }
    stateless_allocator() = default;
    stateless_allocator(const stateless_allocator &) = default;
    stateless_allocator(stateless_allocator &&) = default;
    template <typename U>
    stateless_allocator(const stateless_allocator<U, Impl> &) {}
    bool operator==(const stateless_allocator &) { return true; }
    bool operator!=(const stateless_allocator &) { return false; }
};

template <typename T>
class reversed_container {
public:
    reversed_container(T &base) : base(base) {}
    decltype(std::declval<T>().rbegin()) begin() { return base.rbegin(); }
    decltype(std::declval<T>().crbegin()) begin() const { return base.crbegin(); }
    decltype(std::declval<T>().crbegin()) cbegin() const { return base.crbegin(); }
    decltype(std::declval<T>().rend()) end() { return base.rend(); }
    decltype(std::declval<T>().crend()) end() const { return base.crend(); }
    decltype(std::declval<T>().crend()) cend() const { return base.crend(); }

private:
    T &base;
};

template <typename T>
reversed_container<T> reversed(T &base) {
    return reversed_container<T>(base);
}

template <class T>
static inline void default_new(T *&p) {
    p = new T();
}

template <class T>
static inline void default_new(std::unique_ptr<T> &p) {
    p.reset(new T());
}

struct StringCmp {
    using is_transparent = void;
    bool operator()(std::string_view a, std::string_view b) const { return a < b; }
};

/*
 * Bionic's atoi runs through strtol().
 * Use our own implementation for faster conversion.
 */
int parse_int(std::string_view s);

std::list<std::string> split_str(std::string_view s, std::string_view delimiter);

std::string join_str(const std::list<std::string> &list, std::string_view delimiter);

template <typename T>
static inline T align_to(T v, int a) {
    static_assert(std::is_integral<T>::value);
    return (v + a - 1) / a * a;
}
