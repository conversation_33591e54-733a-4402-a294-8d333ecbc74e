name: Bug report/反馈 Bug
description: Report errors or unexpected behavior./反馈错误或异常行为。
labels: [bug]
title: "[Bug] Short description/简单描述"
body:
  - type: markdown
    attributes:
      value: |
        Thanks for reporting issues for NeoZygisk!
        To make it easier for us to help you please enter detailed information below.

        感谢给 NeoZygisk 汇报问题！
        为了使我们更好地帮助你，请提供以下信息。
        为了防止重复汇报，标题请务必使用英文。
  - type: textarea
    attributes:
      label: Steps to reproduce/复现步骤
      placeholder: |
        1.
        2.
        3.
    validations:
      required: true
  - type: textarea
    attributes:
      label: Expected behaviour/预期行为
      placeholder: Tell us what should happen/正常情况下应该发生什么
    validations:
      required: true
  - type: textarea
    attributes:
      label: Actual behaviour/实际行为
      placeholder: Tell us what happens instead/实际上发生了什么
    validations:
      required: true
  - type: input
    attributes:
      label: Root implementation/Root 方案
      description: Common root implementations include Magisk, KernelSU or APatch. Please specify your current implementation with exact version./常见的 Root 方案有 Magisk，KernelSU 以及 APatch。请注明您当前的方案以及详细版本。
    validations:
      required: true
  - type: textarea
    attributes:
      label: System Module List/系统模块列表
      description: Modules installed through your root implementation manager/通过您 Root 方案的管理器所安装的模块
      render: Shell
    validations:
      required: true
  - type: input
    attributes:
      label: NeoZygisk version/NeoZygisk 版本
      description: Don't use 'latest'. Specify actual version, otherwise your issue will be closed./不要填用“最新版”。给出具体版本号，不然 issue 会被关闭。
    validations:
      required: true
  - type: input
    attributes:
      label: Android version/Android 版本
      description: If you are running a custom OS, please also note it./如果使用了非官方的操作系统，请一并注明。
    validations:
      required: true
  - type: checkboxes
    id: latest
    attributes:
      label: Version requirement/版本要求
      options:
        - label: I am using the latest debug build from [GitHub Actions](https://github.com/JingMatrix/NeoZygisk/actions)./我正在使用 [GitHub Actions](https://github.com/JingMatrix/NeoZygisk/actions) 中最新的调试版本。
          required: true
  - type: textarea
    attributes:
      label: Logs/日志
      description: For usage issues, please provide the log zip saved from LSPosed or your root implementation manager; for activation issues, please provide [bugreport](https://developer.android.com/studio/debug/bug-report). Without logs zip, the issue will be closed. /使用问题请提供从 LSPosed 或您的 root 方案管理器保存的日志压缩包；激活问题请提供 [bugreport](https://developer.android.google.cn/studio/debug/bug-report?hl=zh-cn) 日志。没有日志附件的问题会被关闭。
      placeholder: Upload logs zip by clicking the bar on the bottom. Upload logs to other websites or using external links is prohibited. /点击文本框底栏上传日志压缩包，禁止上传到其它网站或使用外链提供日志。
    validations:
      required: true
